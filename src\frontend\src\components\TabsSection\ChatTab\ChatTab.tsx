import React, { useEffect, useState, useCallback } from "react";
import {
  Chat24Regular,
  Delete24Regular,
  SpinnerIos20Regular,
} from "@fluentui/react-icons";
import { useNavigate } from "react-router";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { getChatHistory } from "../../../utils/getChatHistory";
import { setCurrentChatId } from "../../../features/currentChatIdSlice";
import { clearMessages } from "../../../features/chatSlice";
import { clearFollowUpQuestions } from "../../../features/followUpQuestionsSlice";
import { deleteData, getAllData } from "../../../db/chatDB";
import {Stores} from "../../../constants/dbConstants"
import { decryptionHandler } from "../../../utils/decryptionHandler";
import { clearRecentChat } from "../../../features/recentChatSlice";
import { clearCompleteMessage } from "../../../features/completeMessageSlice";
import { setCurrentWorkspaceId } from "../../../features/currentWorkspaceIdSlice";
import { closePanel } from "../../../features/pdfViewerSlice";
import { useAuth } from "../../../hooks/useAuth";
import Styles from "./ChatTab.module.css";
import { AdjustableWindow } from "../../../hooks/useAdjustableWindow";
import { setCurrentChatLabel } from "../../../features/currentChatLabelSlice";
import { MAX_RESOLUTION_FOR_MOBILE } from "../../../constants/HeaderConstants";
import { setChatHistoryOpen } from "../../../features/chatHistoryOpenSlice";

const ChatTab: React.FC = React.memo(() => {
  const navigate = useNavigate();
  const chatTabs = useSelector((state: RootState) => state.recentChat);
  const email = useSelector((state: RootState) => state.email.email);
  const { acquireToken } = useAuth();
  const apiCallFlag = useSelector(
    (state: RootState) => state.apiCallFlag.status
  );
  const [isLoading, setIsLoading] = useState(true);
  const { width: sidebarWidth, handleResizeStart } = AdjustableWindow({});
  const dispatch = useDispatch();

  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE);

  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const fetch = async () => {
      const allDbData = await getAllData(Stores.Users);
      if (chatTabs.length === 0 && allDbData && allDbData.length > 0) {
        setIsLoading(true);
      }
      if (chatTabs.length === 0 && allDbData && allDbData.length === 0) {
        setIsLoading(false);
      }
      if (chatTabs.length !== 0) {
        setIsLoading(false);
      }
    };
    fetch();
  }, [chatTabs]);

  const currentChatId = useSelector(
    (state: RootState) => state.currentChatId.id
  );

  useEffect(() => {
    const selectedChat = chatTabs.find((chat) => chat.id === currentChatId);
    if (selectedChat) {
      dispatch(setCurrentChatLabel(selectedChat.text));
    } else {
      dispatch(setCurrentChatLabel(""));
    }
  }, [chatTabs, currentChatId, dispatch]);

  const handleChatButtonClick = useCallback(
    async (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      const chatId = (event.target as HTMLButtonElement).id;
      const getToken = await acquireToken();
      if (!getToken?.accessToken) {
        console.error("Failed to acquire token in handleChatButtonClick");
        return;
      }
      setIsLoading(true);
      dispatch(clearFollowUpQuestions());
      await getChatHistory(chatId, email, getToken?.accessToken, dispatch);
      dispatch(setCurrentChatId(chatId));
      dispatch(closePanel());
      setIsLoading(false);
      if (window.innerWidth < MAX_RESOLUTION_FOR_MOBILE) {
        dispatch(setChatHistoryOpen(false));
      }
    },
    [acquireToken, email, dispatch]
  );

  const handleNewChat = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.preventDefault();
      navigate("/");
      dispatch(setCurrentChatId(""));
      dispatch(clearMessages());
      dispatch(clearFollowUpQuestions());
      dispatch(setCurrentWorkspaceId(""));
      dispatch(closePanel());
      if (window.innerWidth < MAX_RESOLUTION_FOR_MOBILE) {
        dispatch(setChatHistoryOpen(false));
      }
    },
    [navigate, dispatch]
  );

  const handleDeleteChat = useCallback(
    async (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      const chatId = (event.currentTarget as HTMLButtonElement).id;
      const getToken = await acquireToken();

      if (chatId === currentChatId) {
        dispatch(clearMessages());
        dispatch(clearCompleteMessage());
        dispatch(setCurrentWorkspaceId(""));
      }
      await deleteData(Stores.Users, chatId);
      if (!getToken?.accessToken) {
        console.error("Failed to acquire token in handleDeleteChat");
        return;
      }
      await decryptionHandler(email, getToken?.accessToken, dispatch);
      const allDbData = await getAllData(Stores.Users);
      if (allDbData.length === 0) {
        dispatch(clearRecentChat());
        dispatch(setCurrentChatId(""));
      }
    },
    [acquireToken, email, currentChatId, dispatch]
  );

  return (
    <div className="h-full flex flex-col lg:flex-row bg-white dark:bg-zinc-800 dark:text-white">
      <div
        className={`flex relative flex-col h-full p-4 space-y-4 overflow-y-auto ${
          sidebarWidth ? "lg:w-auto" : "w-full"
        }`}
        style={
          window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE
            ? { width: sidebarWidth }
            : {}
        }
      >
        <div
          className={`lg:p-4 lg:mt-[56px] lg:overflow-y-auto lg:fixed lg:top-0 lg:left-0 lg:right-0 ${
            !isLoading ? "lg:max-h-[calc(100vh-4rem)]" : "max-h-none"
          }`}
          style={isLargeScreen ? { maxWidth: sidebarWidth } : {}}
        >
          <button
            className={`group w-full flex items-center p-2 rounded dark:bg-zinc-700 ${
              apiCallFlag
                ? "bg-gallagher-blue-50 dark:bg-gallagher-blue-700 cursor-not-allowed"
                : "bg-gallagher-blue-50 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500 cursor-pointer"
            } flex`}
            onClick={handleNewChat}
            disabled={apiCallFlag}
            aria-label="Start New Chat"
            title="Start a New Chat"
          >
            <Chat24Regular
              className={`mr-2 dark:text-white ${
                apiCallFlag
                  ? "text-gallagher-dark-100"
                  : "text-gallagher-dark-100 group-hover:text-gallagher-dark-200 dark:group-hover:text-white"
              }`}
            />
            New Chat
          </button>
          {isLoading && (
            <div className="flex justify-center items-center h-screen">
              <SpinnerIos20Regular
                className={`${Styles.spinner} h-8 w-8 text-gallagher-blue-50`}
              />
            </div>
          )}
          {chatTabs.length > 0 && (
            <h2 className="text-lg font-semibold block mt-4 mb-2">Recent chats</h2>
          )}
          <div className="flex flex-col space-y-2">
            {chatTabs.length > 0 &&
              chatTabs.map((chat) => (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between p-2 rounded cursor-pointer 
                    ${
                      chat.id === currentChatId
                        ? "dark:bg-gallagher-blue-500 bg-gallagher-blue-200"
                        : "dark:bg-zinc-700 bg-gallagher-blue-50"
                    } 
                    hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-400`}
                >
                  <Chat24Regular
                    className={`text-xl text-gallagher-dark-100 group-hover:text-gallagher-dark-200 dark:group-hover:text-gray-300 dark:text-white ${
                      chat.id === currentChatId ? "text-gallagher-dark-200" : ""
                    }`}
                  />
                  <button
                    id={chat.id}
                    className="flex-1 ml-2 pr-2 text-base sm:text-sm truncate text-left break-words cursor-pointer"
                    onClick={handleChatButtonClick}
                    aria-label="Select Conversation"
                    title="Select Conversation"
                  >
                    {chat.text}
                  </button>
                  <button
                    id={chat.id}
                    className="text-xl text-gallagher-dark-100 hover:text-gallagher-dark-200 dark:text-white dark:hover:text-gray-300 cursor-pointer"
                    onClick={handleDeleteChat}
                    aria-label="Delete Chat"
                    title="Delete chat"
                  >
                    <Delete24Regular />
                  </button>
                </div>
              ))}
          </div>
        </div>
      </div>
      <div
        onMouseDown={handleResizeStart}
        className="w-[4px] cursor-col-resize bg-gray-300 dark:bg-zinc-700 hidden lg:block"
      />

      <div className="grow bg-white"></div>
    </div>
  );
});

export default ChatTab;