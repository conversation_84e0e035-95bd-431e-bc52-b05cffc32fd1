import { ChatMessageProps, ChatTabsProps, EncryptedDataProps } from "./types";
import { clearRecentChat, setRecentChat } from "../features/recentChatSlice"; 
import { AppDispatch } from "../store"; 
import { CHAT_TAB_TEXT_LENGTH } from "../constants/constants";

export const recentChatTabsData = async (encryptedData: EncryptedDataProps[], messages:ChatMessageProps[][],dispatch: AppDispatch): Promise<ChatTabsProps[]> => {
    
    if (encryptedData.length === 0) return [];
    if (messages.length === 0) return [];
    const transformedTabData = transformChatData(encryptedData as EncryptedDataProps[], messages as ChatMessageProps[][]);
    
    dispatch(clearRecentChat());
    transformedTabData.forEach(chat => {
        dispatch(setRecentChat(chat));
        
    });

    return transformedTabData;
}

const transformChatData = (dbData: EncryptedDataProps[], decryptedData: ChatMessageProps[][]): ChatTabsProps[] => {
    const temp: ChatTabsProps[] = [];
    for (let i = 0; i < dbData.length; i++) {
        if (decryptedData[i] && decryptedData[i].length > 0) {
            let text = decryptedData[i][0].text;
            if (text.length > CHAT_TAB_TEXT_LENGTH) {
                text = text.substring(0, CHAT_TAB_TEXT_LENGTH) + "...";
            }
            temp.push({ id: dbData[i].id, text });
        }
    }
    return temp.reverse();
}
