import { useState, useEffect, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, updateLastMessage, clearMessages } from "../features/chatSlice";
import { chatService } from "../services/chatService";
import { fetchWorkspaces } from "../services/workspacesService";
import { RootState } from "../store";
import { BackendMessage, Workspace } from "../interfaces";
import { ChatMessageProps } from "../utils/types";
import { useAuth } from "../hooks/useAuth";
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import chatSettings from "../chatSettings";
import { CHAT_MSSG_ROLE_ASSISTANT, CHAT_MSSG_ROLE_USER, IMAGE_INPUT_TYPES, IMAGE_MAX_FILE_SIZE, MAX_INPUT_IMAGE, MESSAGES_LIIMIT } from "../constants/constants";
import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
import DOMPurify from 'dompurify';
import { extractFromMessageUtils } from "../utils/extractFromMessageUtils";
import {  getUserSetting, initUserSettingDB } from "../db/settingDB";
// import { SYSTEM_TEMPLATE } from "../constants/SettingsTabConstants";
import { USER_SETTINGS_ID } from "../constants/dbConstants";

const useChatInput = ({ onMessageComplete }: { onMessageComplete: (messageIndex: number) => void }) => {
  const [input, setInput] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [selectedOption, setSelectedOption] = useState("");
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const dispatch = useDispatch();
  const messages = useSelector((state: RootState) => state.chat.messages);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [notImageUpload, setNotImageUpload] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [template, setTemplate] = useState("");
  const currentWorkspaceId = useSelector((state: RootState) => state.currentWorkspaceId.id);
  const [imageList, setImageList] = useState<string[]>([]);
  const [imageAttachmentDisable, setImageAttachmentDisable] = useState(false);
  const completeMessage = useSelector((state: RootState) => state.completeMessage);
  const userSettings = useSelector((state: RootState) => state.settings);
  const newSessionFlag = useSelector((state: RootState) => state.sessionFlag.status);
  const { acquireToken, activeAccount } = useAuth();

  const setInputValue = useCallback((value: string) => {
    setInput(prev => {
      if (prev !== value) {
        return value;
      }
      return prev;
    });
  }, []);

  const userMessageCount = messages.filter((msg: ChatMessageProps) => msg.user === CHAT_MSSG_ROLE_USER).length;
  const assistantMessageCount = messages.filter((msg: ChatMessageProps) => msg.user === CHAT_MSSG_ROLE_ASSISTANT).length;
  const messageLimitReached = userMessageCount >= MESSAGES_LIIMIT && assistantMessageCount >= MESSAGES_LIIMIT;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageUploading(true);
    if (!e.target.files) {
      setImageUploading(false);
      return;
    }
    const file = e.target.files[0];
    if ((file && !IMAGE_INPUT_TYPES.includes(file.type)) || (file && file.size >= IMAGE_MAX_FILE_SIZE)) {
      setNotImageUpload(true);
      setImageUploading(false);
      return;
    }
    setNotImageUpload(false);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        if (base64String) {
          setImageList((prevList) => [...prevList, base64String]);
          setImageUploading(false);
        }
      };
      reader.readAsDataURL(file);
    }
    (e.target as HTMLInputElement).value = '';
  };

  const handleRemoveImg = (imageString: string, index: number) => {
    setNotImageUpload(false);
    setImageList((prevList) => {
      const newList = [...prevList];
      if (newList[index] === imageString) {
        newList.splice(index, 1);
      }
      return newList;
    });
  };

  useEffect(() => {
    setSelectedOption(currentWorkspaceId);
  }, [currentWorkspaceId]);

  useEffect(() => {
    async function fetchData() {
      await initUserSettingDB();
      const userSettings = await getUserSetting(USER_SETTINGS_ID);
      if(userSettings){
        const template = userSettings.template;
        setTemplate(template || "");
      }
    };
    fetchData();
  }, [template, newSessionFlag]);

  useEffect(() => {
    if (imageList.length === MAX_INPUT_IMAGE) {
      setImageAttachmentDisable(true);
    } else {
      setImageAttachmentDisable(false);
    }
  }, [imageList]);

  useEffect(() => {
    const getWorkspaces = async () => {
      const getToken = await acquireToken();
      if (!activeAccount || !getToken) return;

      try {
        const workspaceData = await fetchWorkspaces(getToken.accessToken);
        setWorkspaces(workspaceData);
      } catch (error) {
        console.error("Error fetching workspaces:", error);
      }
    };

    getWorkspaces();
  }, [acquireToken, activeAccount]);

  useEffect(() => {
    Object.keys(completeMessage).forEach((key) => {
      onMessageComplete(Number(key));
    });
  }, [completeMessage]);

  const sendChatMessage = useCallback(async () => {
    if (messageLimitReached) return;

    const getToken = await acquireToken();
    if (input.trim() === "") return;
    setNotImageUpload(false);
    let inputValue = input;
    const inputArray = [{ type: "text", value: input }];
    if (imageList.length > 0) {
      const imageTags = imageList.map(base64String => {
        const sanitizedBase64String = DOMPurify.sanitize(base64String);
        const obj = { type: "image", value: sanitizedBase64String };
        inputArray.push(obj);
        return `<img src="${sanitizedBase64String}" class="img-list" />`;
      });
      const imageString = imageTags.join("");
      inputValue = input + "\n" + imageString;
    }
    setImageList([]);
    const messageId = messages.length;
    dispatch(addMessage({ user: CHAT_MSSG_ROLE_USER, text: inputValue, messageId, isStreamComplete: true, workspaceId: currentWorkspaceId, documentId: "" }));
    dispatch(addMessage({ user: CHAT_MSSG_ROLE_ASSISTANT, text: "", messageId, isStreamComplete: false, workspaceId: currentWorkspaceId, documentId: "" }));
    setInput("");
    dispatch(setApiCallFlag(true));

    try {
      if (!getToken?.accessToken) throw new Error("Access token acquisition failed.");

      const backendMessages: BackendMessage[] = messages.map((msg: ChatMessageProps) => {
        const { parsedText } = msg.user === CHAT_MSSG_ROLE_ASSISTANT 
          ? extractFromMessageUtils.parseContent(msg.text, msg.messageId)
          : { parsedText: msg.text };
        
        const textWithoutImages = parsedText.replace(/<img[^>]*>/g, '');
        const contentArray = [{ type: "text", value: textWithoutImages.trim() }];

        if (msg.user === CHAT_MSSG_ROLE_USER) {
          const imgTagRegex = /<img[^>]+src="([^">]+)"/g;
          let match;
          while ((match = imgTagRegex.exec(msg.text)) !== null) {
            const src = match[1];
            contentArray.push({ type: "image", value: src });
          }
        }
        return {
          role: msg.user === CHAT_MSSG_ROLE_USER ? CHAT_MSSG_ROLE_USER : CHAT_MSSG_ROLE_ASSISTANT,
          content: contentArray,
        };
      });
      
      
      const model = {
        "openAI": {
          temperature: userSettings.temperature,
          top_p: userSettings.topP,
          frequency_penalty: userSettings.frequencyPenalty,
          presence_penalty: userSettings.presencePenalty,
        }
      };
      let settings = {
        ...chatSettings,
        ...(selectedOption && { workspace_id: selectedOption }), ...model
      };
      if (template) {
        settings = { ...settings, prompt_template: template };
      }

      await chatService(
        currentWorkspaceId,
        settings,
        backendMessages.concat({ role: CHAT_MSSG_ROLE_USER, content: inputArray }),
        (chunk: string) => {
          dispatch(updateLastMessage({ user: CHAT_MSSG_ROLE_ASSISTANT, index: messageId + 1, text: chunk }));
        },
        getToken?.accessToken
      );

      onMessageComplete(messageId + 1);
    } catch (error) {
      console.error("Error sending message:", error);
      dispatch(updateLastMessage({ user: CHAT_MSSG_ROLE_ASSISTANT, index: messageId + 1, text: "Sorry, something went wrong." }));
      onMessageComplete(messageId + 1);
    } finally {
      dispatch(setApiCallFlag(false));
    }
  }, [input, imageList, messages, acquireToken, dispatch, userSettings, selectedOption, currentWorkspaceId, messageLimitReached, onMessageComplete]);

  const handleOptionChange = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setCurrentWorkspaceId(event.target.value));
    setSelectedOption(event.target.value);
    dispatch(setCurrentChatId(""));
    dispatch(clearMessages());
  }, [dispatch]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  }, [sendChatMessage]);

  const openFileDialog = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  const handleFocus = useCallback(() => setIsFocused(true), []);
  const handleBlur = useCallback(() => setIsFocused(false), []);

  return {
    input,
    isFocused,
    selectedOption,
    workspaces,
    fileInputRef,
    notImageUpload,
    imageUploading,
    imageList,
    imageAttachmentDisable,
    messageLimitReached,
    handleImageChange,
    handleRemoveImg,
    handleOptionChange,
    handleInputChange,
    handleKeyDown,
    openFileDialog,
    handleFocus,
    handleBlur,
    sendChatMessage,
    setInputValue,
  };
};

export default useChatInput;