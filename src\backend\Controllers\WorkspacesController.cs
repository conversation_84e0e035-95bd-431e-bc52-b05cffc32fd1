using Backend.Extensions;
using Backend.Models;
using Backend.Models.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;

namespace Backend.Controllers
{
    [Authorize]
    [EnableCors]
    [ApiController]
    [Route("/workspaces")]
    public class WorkspacesController : ControllerBase
    {
        private readonly IStorageService _service;
        private const int MAX_NUMBER_OF_MANAGE_WORKSPACE = 10;

        public WorkspacesController(IStorageService service)
        {
            _service = service;
        }

        // Get Workspaces endpoint
        [HttpGet]
        public async Task<IActionResult> OnGetWorkspacesAsync(
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);

            if (!ct.IsCancellationRequested)
            {
                {
                    var response = await _service.GetWorkspaces(CurrentContext.User.OID!, ct);
                    return Ok(response);
                }
            }
            return new EmptyResult();
        }

        // Get Workspace by id endpoint
        [HttpGet("{id}")]
        public async IAsyncEnumerable<DocumentResult> OnGetWorkspaceAsync(
            string id,
            [EnumeratorCancellation] CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);
            var prefix = CurrentContext.User.OID + "/" + id;

            await foreach (var result in ControllerExtensions.HandleDocumentProcessingAsync<Workspace>(
                () => _service.GetWorkspace(prefix, ct),
                () => _service.GetDocuments(prefix, ct),
                ct))
            {
                yield return result;
                await Task.Delay(1);
            }
        }

        // Create workspace endpoint
        [HttpPost]
        public async Task<IActionResult> OnPostWorkspaceAsync(
            [FromBody] Workspace workspace,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);
            var context = new ValidationContext(workspace) { MemberName = nameof(Workspace.Name) };
            var validation = new RequiredAttribute().GetValidationResult(workspace.Name, context);

            if (validation != ValidationResult.Success)
            {
                return ValidationProblem(validation?.ErrorMessage);
            }

            // Validate workspace limit
            var existingWorkspaces = await _service.GetWorkspaces(CurrentContext.User.OID!, ct);
            if (existingWorkspaces.Count() >= MAX_NUMBER_OF_MANAGE_WORKSPACE)
            {
                return ValidationProblem($"You cannot create more than {MAX_NUMBER_OF_MANAGE_WORKSPACE} workspaces");
            }

            if (!ct.IsCancellationRequested)
            {
                var Result = await _service.CreateWorkspaceAsync(CurrentContext.User.OID!, workspace);
                return Created(String.Empty, Result);
            }
            return new EmptyResult();
        }

        // Edit workspace endpoint
        [HttpPatch("{id?}")]
        public async Task<IActionResult> OnPatchWorkspaceAsync(
            string? id,
            [FromBody] Workspace workspace,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);
            if (id == null && workspace.Id == null) { return ValidationProblem("Id is required."); }
            workspace.Id = (id == null) ? workspace.Id : id;

            if (!ct.IsCancellationRequested)
            {
                var Result = await _service.UpdateWorkspaceAsync(CurrentContext.User.OID!, workspace);
                return Accepted(Result);
            }
            return new EmptyResult();
        }

        // Delete Workspace endpoint
        [HttpDelete("{id}")]
        public async Task<IActionResult> OnDeleteWorkspaceAsync(
            string id,
            CancellationToken ct)
        {
            ControllerExtensions.SetContext(HttpContext);
            if (!ct.IsCancellationRequested)
            {
                var docs = await _service.GetDocuments(CurrentContext.User.OID + "/" + id, ct, true);
                foreach (var doc in docs!)
                {
                    if (ct.IsCancellationRequested) { break; }
                    await _service.DeleteBlobAsync(CurrentContext.User.OID + "/" + id + "/" + doc.Name);
                }
                if (!ct.IsCancellationRequested)
                {
                    await _service.DeleteBlobAsync(CurrentContext.User.OID + "/" + id, false);
                    return NoContent();
                }
            }
            return new EmptyResult();
        }
    }
}
