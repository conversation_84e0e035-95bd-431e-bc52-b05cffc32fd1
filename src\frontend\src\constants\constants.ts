import { <PERSON>K<PERSON> } from "../interfaces";

// Chat constants
export const CHAT_MSSG_ROLE_USER = "user";
export const CHAT_MSSG_ROLE_ASSISTANT = "assistant";
export const CHAT_TAB_TEXT_LENGTH = 50;
export const MAX_CHAT_WINDOW_LENGTH = 10;
export const MANAGE_WORKSPACE_NAME_LIMIT = 256;
export const MANAGE_WORKSPACE_DESCRIPTION_LIMIT = 1000;
export const MAX_NUMBER_OF_MANAGE_WORKSPACE = 10;
export const MAX_NUMBER_OF_DOCUMENTS = 5;
export const MESSAGES_LIIMIT = 25;
export const MESSAGES_LIMIT_ALERT = "Sorry, this conversation has reached its limit. Please start a new chat.";

//Drag and drop constants
export const FILE_TYPE_ACCEPTANCE = [".pdf", ".doc", ".docx", ".ppt", ".pptx"];
export const FILE_MIME_TYPES = {
  "application/pdf": [],
  "application/msword": [],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [],
  "application/vnd.ms-powerpoint": [],
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":[],
};

// PDF constants
export const MAX_FILE_SIZE = 40 * 1024 * 1024; // 40MB
export const MAX_PAGES = 350;

//Disclaimer Data
export const DISCLAIMER_HEADING = "Gallagher AI Disclaimer";
export const DISCLAIMER_DATA = [
  "Interact with our approved and secure AI tool, Gallagher AI to process internal and client information. Only upload necessary personal data to Gallagher AI to comply with data minimization requirements. Publicly available AI tools are insecure and are not approved for use with any Gallagher information.",
  "Output from Gallagher AI is Gallagher proprietary information. Handle it in compliance with our Global Information Classification and Handling Policy, including limitations on sharing as applicable.",
  "We cannot guarantee accuracy of information generated from Gallagher AI. Verify and validate the accuracy of the output before relying upon it for any decisions or actions.",
  "Information uploaded to Gallagher AI, may be transferred internationally. Do not upload information to Gallagher AI if that would breach client contractual obligations that impose data localization requirements.",
  "You can store a maximum of 5 documents within Gallagher AI. Pose questions related to those documents or manually delete and replace them with others. Documents will be automatically removed after 6 months. We may change document limits or storage times.",
  "By using Gallagher AI, you acknowledge and accept these terms and conditions.",
];
export const AJG_ONE_URL = "https://ajg0.sharepoint.com/sites/GO";

//input image upload
export const MAX_INPUT_IMAGE = 5;
export const IMAGE_INPUT_ACCEPTANCE = ".jpg,.jpeg,.png,.webp,.gif";
export const IMAGE_INPUT_TYPES = ["image/jpeg", "image/png", "image/webp", "image/gif"];
export const IMAGE_MAX_FILE_SIZE = 1 * 1024 * 1024; // 1MB
export const IMAGE_ERROR_MESSAGE = "Supported files: .jpg, .jpeg, .png, .webp & .gif | Max size: 1MB"

// Region configurable labels
export const REGION_LABELS: Record<RegionKey, string> = {
  LOCALE: "U.S.",
  US: "U.S.",
  CN: "Canada",
  UK: "UK",
  AU: "AU",
};

// DocumentsList constants
export const FILE_NOT_PROCESSED_ERROR = "File cannot be processed currently. Please delete.";